const Like = require('../models/Like');
const Post = require('../models/Post');
const ResponseController = require('./responseController');

/**
 * Like Controller
 * Handles like-related operations
 */

class LikeController {
  /**
   * Toggle like on a post
   * @route POST /api/posts/:postId/like
   * @access Private
   */
  static async toggleLike(req, res) {
    try {
      const { postId } = req.params;
      const { type = 'like' } = req.body;
      const userId = req.user._id;
      const tenantId = req.user.currentTenant;

      if (!tenantId) {
        return ResponseController.error(res, 'Please select a tenant', 400);
      }

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      // Check if user has access to this post
      if (post.visibility === 'private' && post.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'Access denied to this post');
      }

      const result = await Like.toggleLike(userId, postId, tenantId, type);

      return ResponseController.success(res, `Post ${result.action} successfully`, {
        action: result.action,
        like: result.like
      });

    } catch (error) {
      console.error('Toggle like error:', error);
      return ResponseController.serverError(res, 'Error toggling like');
    }
  }

  /**
   * Get likes for a post
   * @route GET /api/posts/:postId/likes
   * @access Private
   */
  static async getPostLikes(req, res) {
    try {
      const { postId } = req.params;
      const { page = 1, limit = 20, type } = req.query;

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      const likes = await Like.getPostLikes(postId, {
        page: parseInt(page),
        limit: parseInt(limit),
        type
      });

      const total = await Like.countDocuments({
        post: postId,
        isActive: true,
        ...(type && { type })
      });

      return ResponseController.success(res, 'Post likes retrieved successfully', {
        likes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get post likes error:', error);
      return ResponseController.serverError(res, 'Error retrieving post likes');
    }
  }

  /**
   * Get like statistics for a post
   * @route GET /api/posts/:postId/likes/stats
   * @access Private
   */
  static async getLikeStats(req, res) {
    try {
      const { postId } = req.params;

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      const stats = await Like.getLikeStats(postId);

      return ResponseController.success(res, 'Like statistics retrieved successfully', {
        stats
      });

    } catch (error) {
      console.error('Get like stats error:', error);
      return ResponseController.serverError(res, 'Error retrieving like statistics');
    }
  }

  /**
   * Get user's liked posts
   * @route GET /api/users/:userId/likes
   * @access Private
   */
  static async getUserLikes(req, res) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const currentUserId = req.user._id;
      const tenantId = req.user.currentTenant;

      // Check if user is requesting their own likes or if they have permission
      if (userId !== currentUserId.toString()) {
        // You could add additional permission checks here
        // For now, allow viewing other users' likes
      }

      const likes = await Like.getUserLikes(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        tenantId
      });

      const total = await Like.countDocuments({
        user: userId,
        isActive: true,
        ...(tenantId && { tenant: tenantId })
      });

      return ResponseController.success(res, 'User likes retrieved successfully', {
        likes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get user likes error:', error);
      return ResponseController.serverError(res, 'Error retrieving user likes');
    }
  }

  /**
   * Check if user liked a post
   * @route GET /api/posts/:postId/likes/check
   * @access Private
   */
  static async checkUserLike(req, res) {
    try {
      const { postId } = req.params;
      const userId = req.user._id;

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      const like = await Like.findOne({
        user: userId,
        post: postId,
        isActive: true
      });

      return ResponseController.success(res, 'Like status retrieved successfully', {
        isLiked: !!like,
        likeType: like ? like.type : null,
        likedAt: like ? like.createdAt : null
      });

    } catch (error) {
      console.error('Check user like error:', error);
      return ResponseController.serverError(res, 'Error checking like status');
    }
  }

  /**
   * Get recent likes activity
   * @route GET /api/likes/recent
   * @access Private
   */
  static async getRecentLikes(req, res) {
    try {
      const { limit = 10 } = req.query;
      const tenantId = req.user.currentTenant;

      if (!tenantId) {
        return ResponseController.error(res, 'Please select a tenant', 400);
      }

      const recentLikes = await Like.find({
        tenant: tenantId,
        isActive: true
      })
      .populate('user', 'name username avatar')
      .populate('post', 'content author')
      .populate({
        path: 'post',
        populate: {
          path: 'author',
          select: 'name username avatar'
        }
      })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

      return ResponseController.success(res, 'Recent likes retrieved successfully', {
        likes: recentLikes
      });

    } catch (error) {
      console.error('Get recent likes error:', error);
      return ResponseController.serverError(res, 'Error retrieving recent likes');
    }
  }
}

module.exports = LikeController;
