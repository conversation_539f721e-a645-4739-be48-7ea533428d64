const { validationResult } = require('express-validator');
const Post = require('../models/Post');
const Like = require('../models/Like');
const Comment = require('../models/Comment');
const User = require('../models/User');
const ResponseController = require('./responseController');

/**
 * Post Controller
 * Handles post-related operations
 */

class PostController {
  /**
   * Create a new post
   * @route POST /api/posts
   * @access Private
   */
  static async createPost(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { content, type = 'text', visibility = 'tenant', media, link, poll, tags } = req.body;
      const userId = req.user._id;
      const tenantId = req.user.currentTenant;

      if (!tenantId) {
        return ResponseController.error(res, 'Please select a tenant to post in', 400);
      }

      const postData = {
        content,
        author: userId,
        tenant: tenantId,
        type,
        visibility
      };

      if (media && media.length > 0) {
        postData.media = media;
      }

      if (link) {
        postData.link = link;
      }

      if (poll) {
        postData.poll = poll;
      }

      if (tags && tags.length > 0) {
        postData.tags = tags;
      }

      const post = await Post.create(postData);
      
      // Update user's post count
      await User.findByIdAndUpdate(userId, { $inc: { postsCount: 1 } });

      const populatedPost = await Post.findById(post._id)
        .populate('author', 'name username avatar')
        .populate('tenant', 'name slug');

      return ResponseController.created(res, 'Post created successfully', { post: populatedPost });

    } catch (error) {
      console.error('Create post error:', error);
      return ResponseController.serverError(res, 'Error creating post');
    }
  }

  /**
   * Get all posts (feed)
   * @route GET /api/posts
   * @access Private
   */
  static async getPosts(req, res) {
    try {
      const { page = 1, limit = 10, type, visibility, sortBy = 'createdAt' } = req.query;
      const userId = req.user._id;
      const tenantId = req.user.currentTenant;

      if (!tenantId) {
        return ResponseController.error(res, 'Please select a tenant', 400);
      }

      const query = {
        tenant: tenantId,
        isActive: true
      };

      if (type) query.type = type;
      if (visibility) query.visibility = visibility;

      const posts = await Post.find(query)
        .populate('author', 'name username avatar')
        .populate('tenant', 'name slug')
        .sort({ [sortBy]: -1 })
        .skip((page - 1) * limit)
        .limit(parseInt(limit));

      // Add like status for current user
      const postsWithLikeStatus = await Promise.all(
        posts.map(async (post) => {
          const isLiked = await post.isLikedBy(userId);
          return {
            ...post.toObject(),
            isLiked
          };
        })
      );

      const total = await Post.countDocuments(query);

      return ResponseController.success(res, 'Posts retrieved successfully', {
        posts: postsWithLikeStatus,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get posts error:', error);
      return ResponseController.serverError(res, 'Error retrieving posts');
    }
  }

  /**
   * Get single post
   * @route GET /api/posts/:id
   * @access Private
   */
  static async getPost(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const post = await Post.findById(id)
        .populate('author', 'name username avatar bio')
        .populate('tenant', 'name slug');

      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      // Check if user has access to this post
      if (post.visibility === 'private' && post.author._id.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'Access denied to this post');
      }

      // Increment view count
      await Post.findByIdAndUpdate(id, { $inc: { 'stats.viewsCount': 1 } });

      // Check if user liked this post
      const isLiked = await post.isLikedBy(userId);

      const postData = {
        ...post.toObject(),
        isLiked
      };

      return ResponseController.success(res, 'Post retrieved successfully', { post: postData });

    } catch (error) {
      console.error('Get post error:', error);
      return ResponseController.serverError(res, 'Error retrieving post');
    }
  }

  /**
   * Update post
   * @route PUT /api/posts/:id
   * @access Private
   */
  static async updatePost(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { id } = req.params;
      const { content, visibility, tags } = req.body;
      const userId = req.user._id;

      const post = await Post.findById(id);

      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      if (post.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'You can only edit your own posts');
      }

      // Store original content in edit history
      if (post.content !== content) {
        post.editHistory.push({
          content: post.content,
          editedAt: new Date()
        });
        post.isEdited = true;
      }

      // Update post
      post.content = content;
      if (visibility) post.visibility = visibility;
      if (tags) post.tags = tags;

      await post.save();

      const updatedPost = await Post.findById(id)
        .populate('author', 'name username avatar')
        .populate('tenant', 'name slug');

      return ResponseController.success(res, 'Post updated successfully', { post: updatedPost });

    } catch (error) {
      console.error('Update post error:', error);
      return ResponseController.serverError(res, 'Error updating post');
    }
  }

  /**
   * Delete post
   * @route DELETE /api/posts/:id
   * @access Private
   */
  static async deletePost(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const post = await Post.findById(id);

      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      if (post.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'You can only delete your own posts');
      }

      // Soft delete
      post.isActive = false;
      await post.save();

      // Update user's post count
      await User.findByIdAndUpdate(userId, { $inc: { postsCount: -1 } });

      return ResponseController.success(res, 'Post deleted successfully');

    } catch (error) {
      console.error('Delete post error:', error);
      return ResponseController.serverError(res, 'Error deleting post');
    }
  }

  /**
   * Get user's posts
   * @route GET /api/posts/user/:userId
   * @access Private
   */
  static async getUserPosts(req, res) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10 } = req.query;
      const currentUserId = req.user._id;

      const posts = await Post.findByAuthor(userId, {
        page: parseInt(page),
        limit: parseInt(limit)
      });

      // Add like status for current user
      const postsWithLikeStatus = await Promise.all(
        posts.map(async (post) => {
          const isLiked = await post.isLikedBy(currentUserId);
          return {
            ...post.toObject(),
            isLiked
          };
        })
      );

      const total = await Post.countDocuments({ 
        author: userId, 
        isActive: true 
      });

      return ResponseController.success(res, 'User posts retrieved successfully', {
        posts: postsWithLikeStatus,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get user posts error:', error);
      return ResponseController.serverError(res, 'Error retrieving user posts');
    }
  }
}

module.exports = PostController;
