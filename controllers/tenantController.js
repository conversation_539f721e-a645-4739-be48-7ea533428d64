const { validationResult } = require('express-validator');
const Tenant = require('../models/Tenant');
const User = require('../models/User');
const ResponseController = require('./responseController');

/**
 * Tenant Controller
 * Handles tenant-related operations
 */

class TenantController {
  /**
   * Create a new tenant
   * @route POST /api/tenants
   * @access Private
   */
  static async createTenant(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { name, slug, description, website, industry, location, settings } = req.body;
      const userId = req.user._id;

      // Check if slug already exists
      if (slug) {
        const existingTenant = await Tenant.findOne({ slug });
        if (existingTenant) {
          return ResponseController.error(res, 'Tenant slug already exists', 409);
        }
      }

      const tenantData = {
        name,
        description,
        website,
        industry,
        location,
        owner: userId
      };

      if (slug) {
        tenantData.slug = slug;
      }

      if (settings) {
        tenantData.settings = { ...tenantData.settings, ...settings };
      }

      const tenant = await Tenant.create(tenantData);

      // Add user to tenant as owner
      await User.findByIdAndUpdate(userId, {
        $push: {
          tenants: {
            tenant: tenant._id,
            role: 'owner',
            joinedAt: new Date(),
            isActive: true
          }
        },
        currentTenant: tenant._id
      });

      const populatedTenant = await Tenant.findById(tenant._id)
        .populate('owner', 'name username avatar');

      return ResponseController.created(res, 'Tenant created successfully', { tenant: populatedTenant });

    } catch (error) {
      console.error('Create tenant error:', error);
      return ResponseController.serverError(res, 'Error creating tenant');
    }
  }

  /**
   * Get all tenants (public)
   * @route GET /api/tenants
   * @access Public
   */
  static async getTenants(req, res) {
    try {
      const { page = 1, limit = 20, search, industry } = req.query;

      const query = {
        'settings.isPublic': true,
        isActive: true
      };

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      if (industry) {
        query.industry = industry;
      }

      const tenants = await Tenant.find(query)
        .populate('owner', 'name username avatar')
        .sort({ 'stats.membersCount': -1, createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(parseInt(limit));

      const total = await Tenant.countDocuments(query);

      return ResponseController.success(res, 'Tenants retrieved successfully', {
        tenants,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get tenants error:', error);
      return ResponseController.serverError(res, 'Error retrieving tenants');
    }
  }

  /**
   * Get single tenant
   * @route GET /api/tenants/:id
   * @access Public
   */
  static async getTenant(req, res) {
    try {
      const { id } = req.params;

      const tenant = await Tenant.findById(id)
        .populate('owner', 'name username avatar bio');

      if (!tenant || !tenant.isActive) {
        return ResponseController.notFound(res, 'Tenant not found');
      }

      // Check if tenant is public or user has access
      if (!tenant.settings.isPublic && req.user) {
        const user = await User.findById(req.user._id);
        const isMember = user.tenants.some(t => 
          t.tenant.toString() === tenant._id.toString() && t.isActive
        );
        
        if (!isMember) {
          return ResponseController.forbidden(res, 'Access denied to this tenant');
        }
      }

      return ResponseController.success(res, 'Tenant retrieved successfully', { tenant });

    } catch (error) {
      console.error('Get tenant error:', error);
      return ResponseController.serverError(res, 'Error retrieving tenant');
    }
  }

  /**
   * Update tenant
   * @route PUT /api/tenants/:id
   * @access Private
   */
  static async updateTenant(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { id } = req.params;
      const { name, description, website, industry, location, settings } = req.body;
      const userId = req.user._id;

      const tenant = await Tenant.findById(id);

      if (!tenant || !tenant.isActive) {
        return ResponseController.notFound(res, 'Tenant not found');
      }

      // Check if user is owner or admin
      const user = await User.findById(userId);
      const userTenant = user.tenants.find(t => 
        t.tenant.toString() === tenant._id.toString() && t.isActive
      );

      if (!userTenant || !['owner', 'admin'].includes(userTenant.role)) {
        return ResponseController.forbidden(res, 'You do not have permission to update this tenant');
      }

      // Update tenant
      if (name) tenant.name = name;
      if (description) tenant.description = description;
      if (website) tenant.website = website;
      if (industry) tenant.industry = industry;
      if (location) tenant.location = location;
      if (settings) {
        tenant.settings = { ...tenant.settings, ...settings };
      }

      await tenant.save();

      const updatedTenant = await Tenant.findById(id)
        .populate('owner', 'name username avatar');

      return ResponseController.success(res, 'Tenant updated successfully', { tenant: updatedTenant });

    } catch (error) {
      console.error('Update tenant error:', error);
      return ResponseController.serverError(res, 'Error updating tenant');
    }
  }

  /**
   * Join tenant
   * @route POST /api/tenants/:id/join
   * @access Private
   */
  static async joinTenant(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const tenant = await Tenant.findById(id);

      if (!tenant || !tenant.isActive) {
        return ResponseController.notFound(res, 'Tenant not found');
      }

      // Check if tenant allows new members
      if (!tenant.settings.isPublic && !tenant.settings.allowMemberInvites) {
        return ResponseController.forbidden(res, 'This tenant is not accepting new members');
      }

      // Check if user is already a member
      const user = await User.findById(userId);
      const existingMembership = user.tenants.find(t => 
        t.tenant.toString() === tenant._id.toString()
      );

      if (existingMembership) {
        if (existingMembership.isActive) {
          return ResponseController.error(res, 'You are already a member of this tenant', 409);
        } else {
          // Reactivate membership
          existingMembership.isActive = true;
          existingMembership.joinedAt = new Date();
          await user.save();
        }
      } else {
        // Add new membership
        user.tenants.push({
          tenant: tenant._id,
          role: 'member',
          joinedAt: new Date(),
          isActive: true
        });
        await user.save();
      }

      // Update tenant member count
      await Tenant.findByIdAndUpdate(id, {
        $inc: { 'stats.membersCount': 1 }
      });

      // Set as current tenant if user doesn't have one
      if (!user.currentTenant) {
        user.currentTenant = tenant._id;
        await user.save();
      }

      return ResponseController.success(res, 'Successfully joined tenant', { tenant });

    } catch (error) {
      console.error('Join tenant error:', error);
      return ResponseController.serverError(res, 'Error joining tenant');
    }
  }

  /**
   * Leave tenant
   * @route POST /api/tenants/:id/leave
   * @access Private
   */
  static async leaveTenant(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const tenant = await Tenant.findById(id);

      if (!tenant || !tenant.isActive) {
        return ResponseController.notFound(res, 'Tenant not found');
      }

      // Check if user is the owner
      if (tenant.owner.toString() === userId.toString()) {
        return ResponseController.error(res, 'Owner cannot leave the tenant. Transfer ownership first.', 400);
      }

      const user = await User.findById(userId);
      const membershipIndex = user.tenants.findIndex(t => 
        t.tenant.toString() === tenant._id.toString() && t.isActive
      );

      if (membershipIndex === -1) {
        return ResponseController.error(res, 'You are not a member of this tenant', 400);
      }

      // Deactivate membership
      user.tenants[membershipIndex].isActive = false;

      // If this was the current tenant, clear it
      if (user.currentTenant && user.currentTenant.toString() === tenant._id.toString()) {
        user.currentTenant = null;
      }

      await user.save();

      // Update tenant member count
      await Tenant.findByIdAndUpdate(id, {
        $inc: { 'stats.membersCount': -1 }
      });

      return ResponseController.success(res, 'Successfully left tenant');

    } catch (error) {
      console.error('Leave tenant error:', error);
      return ResponseController.serverError(res, 'Error leaving tenant');
    }
  }

  /**
   * Get user's tenants
   * @route GET /api/users/tenants
   * @access Private
   */
  static async getUserTenants(req, res) {
    try {
      const userId = req.user._id;

      const user = await User.findById(userId)
        .populate({
          path: 'tenants.tenant',
          select: 'name slug description logo stats settings',
          match: { isActive: true }
        });

      const activeTenants = user.tenants
        .filter(t => t.isActive && t.tenant)
        .map(t => ({
          ...t.tenant.toObject(),
          userRole: t.role,
          joinedAt: t.joinedAt
        }));

      return ResponseController.success(res, 'User tenants retrieved successfully', {
        tenants: activeTenants,
        currentTenant: user.currentTenant
      });

    } catch (error) {
      console.error('Get user tenants error:', error);
      return ResponseController.serverError(res, 'Error retrieving user tenants');
    }
  }

  /**
   * Switch current tenant
   * @route POST /api/tenants/:id/switch
   * @access Private
   */
  static async switchTenant(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const user = await User.findById(userId);
      const membership = user.tenants.find(t => 
        t.tenant.toString() === id && t.isActive
      );

      if (!membership) {
        return ResponseController.error(res, 'You are not a member of this tenant', 400);
      }

      user.currentTenant = id;
      await user.save();

      const tenant = await Tenant.findById(id);

      return ResponseController.success(res, 'Tenant switched successfully', { tenant });

    } catch (error) {
      console.error('Switch tenant error:', error);
      return ResponseController.serverError(res, 'Error switching tenant');
    }
  }
}

module.exports = TenantController;
