const { validationResult } = require('express-validator');
const Comment = require('../models/Comment');
const Post = require('../models/Post');
const ResponseController = require('./responseController');

/**
 * Comment Controller
 * Handles comment-related operations
 */

class CommentController {
  /**
   * Create a new comment
   * @route POST /api/posts/:postId/comments
   * @access Private
   */
  static async createComment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { postId } = req.params;
      const { content, parentComment, media } = req.body;
      const userId = req.user._id;
      const tenantId = req.user.currentTenant;

      if (!tenantId) {
        return ResponseController.error(res, 'Please select a tenant', 400);
      }

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      // Check if user has access to this post
      if (post.visibility === 'private' && post.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'Access denied to this post');
      }

      // If replying to a comment, check if parent comment exists
      if (parentComment) {
        const parentCommentDoc = await Comment.findById(parentComment);
        if (!parentCommentDoc || !parentCommentDoc.isActive) {
          return ResponseController.notFound(res, 'Parent comment not found');
        }
      }

      const commentData = {
        content,
        author: userId,
        post: postId,
        tenant: tenantId
      };

      if (parentComment) {
        commentData.parentComment = parentComment;
      }

      if (media && media.length > 0) {
        commentData.media = media;
      }

      const comment = await Comment.create(commentData);

      const populatedComment = await Comment.findById(comment._id)
        .populate('author', 'name username avatar')
        .populate('parentComment', 'content author');

      return ResponseController.created(res, 'Comment created successfully', { comment: populatedComment });

    } catch (error) {
      console.error('Create comment error:', error);
      return ResponseController.serverError(res, 'Error creating comment');
    }
  }

  /**
   * Get comments for a post
   * @route GET /api/posts/:postId/comments
   * @access Private
   */
  static async getPostComments(req, res) {
    try {
      const { postId } = req.params;
      const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'asc' } = req.query;

      // Check if post exists
      const post = await Post.findById(postId);
      if (!post || !post.isActive) {
        return ResponseController.notFound(res, 'Post not found');
      }

      const comments = await Comment.getPostComments(postId, {
        page: parseInt(page),
        limit: parseInt(limit),
        sortBy,
        sortOrder: sortOrder === 'desc' ? -1 : 1
      });

      const total = await Comment.countDocuments({
        post: postId,
        parentComment: null,
        isActive: true
      });

      return ResponseController.success(res, 'Comments retrieved successfully', {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get post comments error:', error);
      return ResponseController.serverError(res, 'Error retrieving comments');
    }
  }

  /**
   * Get replies for a comment
   * @route GET /api/comments/:commentId/replies
   * @access Private
   */
  static async getCommentReplies(req, res) {
    try {
      const { commentId } = req.params;
      const { page = 1, limit = 10 } = req.query;

      // Check if comment exists
      const comment = await Comment.findById(commentId);
      if (!comment || !comment.isActive) {
        return ResponseController.notFound(res, 'Comment not found');
      }

      const replies = await Comment.getCommentReplies(commentId, {
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const total = await Comment.countDocuments({
        parentComment: commentId,
        isActive: true
      });

      return ResponseController.success(res, 'Comment replies retrieved successfully', {
        replies,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get comment replies error:', error);
      return ResponseController.serverError(res, 'Error retrieving comment replies');
    }
  }

  /**
   * Update a comment
   * @route PUT /api/comments/:id
   * @access Private
   */
  static async updateComment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return ResponseController.validationError(res, errors.array());
      }

      const { id } = req.params;
      const { content } = req.body;
      const userId = req.user._id;

      const comment = await Comment.findById(id);

      if (!comment || !comment.isActive) {
        return ResponseController.notFound(res, 'Comment not found');
      }

      if (comment.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'You can only edit your own comments');
      }

      // Store original content in edit history
      if (comment.content !== content) {
        comment.editHistory.push({
          content: comment.content,
          editedAt: new Date()
        });
        comment.isEdited = true;
      }

      comment.content = content;
      await comment.save();

      const updatedComment = await Comment.findById(id)
        .populate('author', 'name username avatar')
        .populate('parentComment', 'content author');

      return ResponseController.success(res, 'Comment updated successfully', { comment: updatedComment });

    } catch (error) {
      console.error('Update comment error:', error);
      return ResponseController.serverError(res, 'Error updating comment');
    }
  }

  /**
   * Delete a comment
   * @route DELETE /api/comments/:id
   * @access Private
   */
  static async deleteComment(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user._id;

      const comment = await Comment.findById(id);

      if (!comment || !comment.isActive) {
        return ResponseController.notFound(res, 'Comment not found');
      }

      if (comment.author.toString() !== userId.toString()) {
        return ResponseController.forbidden(res, 'You can only delete your own comments');
      }

      // Soft delete
      comment.isActive = false;
      await comment.save();

      return ResponseController.success(res, 'Comment deleted successfully');

    } catch (error) {
      console.error('Delete comment error:', error);
      return ResponseController.serverError(res, 'Error deleting comment');
    }
  }

  /**
   * Get user's comments
   * @route GET /api/users/:userId/comments
   * @access Private
   */
  static async getUserComments(req, res) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const tenantId = req.user.currentTenant;

      const comments = await Comment.getUserComments(userId, {
        page: parseInt(page),
        limit: parseInt(limit),
        tenantId
      });

      const total = await Comment.countDocuments({
        author: userId,
        isActive: true,
        ...(tenantId && { tenant: tenantId })
      });

      return ResponseController.success(res, 'User comments retrieved successfully', {
        comments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get user comments error:', error);
      return ResponseController.serverError(res, 'Error retrieving user comments');
    }
  }
}

module.exports = CommentController;
