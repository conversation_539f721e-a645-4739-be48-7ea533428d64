const jwt = require('jsonwebtoken');
const User = require('../models/User');
const ResponseController = require('../controllers/responseController');

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from token
      req.user = await User.findById(decoded.id).select('-password');

      if (!req.user) {
        return ResponseController.unauthorized(res, 'User not found');
      }

      if (!req.user.isActive) {
        return ResponseController.unauthorized(res, 'User account is deactivated');
      }

      next();
    } catch (error) {
      console.error('Token verification error:', error);
      return ResponseController.unauthorized(res, 'Not authorized, token failed');
    }
  }

  if (!token) {
    return ResponseController.unauthorized(res, 'Not authorized, no token');
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return ResponseController.forbidden(res, `User role ${req.user.role} is not authorized to access this route`);
    }
    next();
  };
};

module.exports = { protect, authorize };
