/**
 * Simple API Test Script
 * Run this script to test the authentication endpoints
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test data
const testUser = {
  name: '<PERSON>',
  email: '<EMAIL>',
  password: 'Password123',
  confirmPassword: 'Password123'
};

const loginData = {
  email: testUser.email,
  password: testUser.password
};

let authToken = '';

// Helper function to make requests
const makeRequest = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    return error.response ? error.response.data : { error: error.message };
  }
};

// Test functions
const testHealthCheck = async () => {
  console.log('\n🔍 Testing Health Check...');
  const result = await makeRequest('GET', '/health');
  console.log('Health Check Result:', JSON.stringify(result, null, 2));
};

const testRegister = async () => {
  console.log('\n📝 Testing User Registration...');
  const result = await makeRequest('POST', '/auth/register', testUser);
  console.log('Registration Result:', JSON.stringify(result, null, 2));
  
  if (result.success && result.data && result.data.token) {
    authToken = result.data.token;
    console.log('✅ Registration successful! Token saved.');
  } else {
    console.log('❌ Registration failed!');
  }
};

const testLogin = async () => {
  console.log('\n🔐 Testing User Login...');
  const result = await makeRequest('POST', '/auth/login', loginData);
  console.log('Login Result:', JSON.stringify(result, null, 2));
  
  if (result.success && result.data && result.data.token) {
    authToken = result.data.token;
    console.log('✅ Login successful! Token updated.');
  } else {
    console.log('❌ Login failed!');
  }
};

const testProfile = async () => {
  console.log('\n👤 Testing Get Profile...');
  if (!authToken) {
    console.log('❌ No auth token available. Skipping profile test.');
    return;
  }
  
  const result = await makeRequest('GET', '/auth/profile', null, authToken);
  console.log('Profile Result:', JSON.stringify(result, null, 2));
};

const testUpdateProfile = async () => {
  console.log('\n✏️ Testing Update Profile...');
  if (!authToken) {
    console.log('❌ No auth token available. Skipping profile update test.');
    return;
  }
  
  const updateData = { name: 'John Updated Doe' };
  const result = await makeRequest('PUT', '/auth/profile', updateData, authToken);
  console.log('Profile Update Result:', JSON.stringify(result, null, 2));
};

const testChangePassword = async () => {
  console.log('\n🔑 Testing Change Password...');
  if (!authToken) {
    console.log('❌ No auth token available. Skipping password change test.');
    return;
  }
  
  const passwordData = {
    currentPassword: testUser.password,
    newPassword: 'NewPassword123',
    confirmNewPassword: 'NewPassword123'
  };
  
  const result = await makeRequest('PUT', '/auth/change-password', passwordData, authToken);
  console.log('Password Change Result:', JSON.stringify(result, null, 2));
};

const testInvalidEndpoint = async () => {
  console.log('\n❓ Testing Invalid Endpoint...');
  const result = await makeRequest('GET', '/invalid-endpoint');
  console.log('Invalid Endpoint Result:', JSON.stringify(result, null, 2));
};

// Run all tests
const runTests = async () => {
  console.log('🚀 Starting API Tests...');
  console.log('='.repeat(50));
  
  await testHealthCheck();
  await testRegister();
  await testLogin();
  await testProfile();
  await testUpdateProfile();
  await testChangePassword();
  await testInvalidEndpoint();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ All tests completed!');
};

// Check if axios is available
if (typeof require !== 'undefined') {
  runTests().catch(error => {
    console.error('Test execution error:', error.message);
  });
} else {
  console.log('❌ This script requires Node.js and axios package.');
  console.log('Run: npm install axios');
}
