const express = require('express');
const TenantController = require('../controllers/tenantController');
const TenantValidator = require('../validators/tenantValidator');
const { validate } = require('../validators');
const { protect } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all tenants (public)
// @route   GET /api/tenants
// @access  Public
router.get('/', 
  TenantController.getTenants
);

// @desc    Get single tenant
// @route   GET /api/tenants/:id
// @access  Public
router.get('/:id', 
  validate(TenantValidator.tenantIdValidation()), 
  TenantController.getTenant
);

// Protected routes (require authentication)
router.use(protect);

// @desc    Create a new tenant
// @route   POST /api/tenants
// @access  Private
router.post('/', 
  validate(TenantValidator.createTenantValidation()), 
  TenantController.createTenant
);

// @desc    Update tenant
// @route   PUT /api/tenants/:id
// @access  Private
router.put('/:id', 
  validate(TenantValidator.updateTenantValidation()), 
  TenantController.updateTenant
);

// @desc    Join tenant
// @route   POST /api/tenants/:id/join
// @access  Private
router.post('/:id/join', 
  validate(TenantValidator.tenantIdValidation()), 
  TenantController.joinTenant
);

// @desc    Leave tenant
// @route   POST /api/tenants/:id/leave
// @access  Private
router.post('/:id/leave', 
  validate(TenantValidator.tenantIdValidation()), 
  TenantController.leaveTenant
);

// @desc    Switch current tenant
// @route   POST /api/tenants/:id/switch
// @access  Private
router.post('/:id/switch', 
  validate(TenantValidator.tenantIdValidation()), 
  TenantController.switchTenant
);

// @desc    Get user's tenants
// @route   GET /api/tenants/user/my-tenants
// @access  Private
router.get('/user/my-tenants', 
  TenantController.getUserTenants
);

module.exports = router;
