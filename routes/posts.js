const express = require('express');
const PostController = require('../controllers/postController');
const LikeController = require('../controllers/likeController');
const CommentController = require('../controllers/commentController');
const PostValidator = require('../validators/postValidator');
const { validate } = require('../validators');
const { protect } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// Post routes
// @desc    Create a new post
// @route   POST /api/posts
// @access  Private
router.post('/', 
  validate(PostValidator.createPostValidation()), 
  PostController.createPost
);

// @desc    Get all posts (feed)
// @route   GET /api/posts
// @access  Private
router.get('/', 
  PostController.getPosts
);

// @desc    Get single post
// @route   GET /api/posts/:id
// @access  Private
router.get('/:id', 
  validate(PostValidator.postIdValidation()), 
  PostController.getPost
);

// @desc    Update post
// @route   PUT /api/posts/:id
// @access  Private
router.put('/:id', 
  validate(PostValidator.updatePostValidation()), 
  PostController.updatePost
);

// @desc    Delete post
// @route   DELETE /api/posts/:id
// @access  Private
router.delete('/:id', 
  validate(PostValidator.postIdValidation()), 
  PostController.deletePost
);

// @desc    Get user's posts
// @route   GET /api/posts/user/:userId
// @access  Private
router.get('/user/:userId', 
  validate(PostValidator.userIdValidation()), 
  PostController.getUserPosts
);

// Like routes
// @desc    Toggle like on a post
// @route   POST /api/posts/:postId/like
// @access  Private
router.post('/:postId/like', 
  validate(PostValidator.likeValidation()), 
  LikeController.toggleLike
);

// @desc    Get likes for a post
// @route   GET /api/posts/:postId/likes
// @access  Private
router.get('/:postId/likes', 
  validate(PostValidator.postIdValidation()), 
  LikeController.getPostLikes
);

// @desc    Get like statistics for a post
// @route   GET /api/posts/:postId/likes/stats
// @access  Private
router.get('/:postId/likes/stats', 
  validate(PostValidator.postIdValidation()), 
  LikeController.getLikeStats
);

// @desc    Check if user liked a post
// @route   GET /api/posts/:postId/likes/check
// @access  Private
router.get('/:postId/likes/check', 
  validate(PostValidator.postIdValidation()), 
  LikeController.checkUserLike
);

// Comment routes
// @desc    Create a comment on a post
// @route   POST /api/posts/:postId/comments
// @access  Private
router.post('/:postId/comments', 
  validate(PostValidator.createCommentValidation()), 
  CommentController.createComment
);

// @desc    Get comments for a post
// @route   GET /api/posts/:postId/comments
// @access  Private
router.get('/:postId/comments', 
  validate(PostValidator.postIdValidation()), 
  CommentController.getPostComments
);

module.exports = router;
