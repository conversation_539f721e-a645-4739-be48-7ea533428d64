const express = require('express');
const CommentController = require('../controllers/commentController');
const PostValidator = require('../validators/postValidator');
const { validate } = require('../validators');
const { protect } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(protect);

// @desc    Update a comment
// @route   PUT /api/comments/:id
// @access  Private
router.put('/:id', 
  validate(PostValidator.updateCommentValidation()), 
  CommentController.updateComment
);

// @desc    Delete a comment
// @route   DELETE /api/comments/:id
// @access  Private
router.delete('/:id', 
  validate(PostValidator.commentIdValidation()), 
  CommentController.deleteComment
);

// @desc    Get replies for a comment
// @route   GET /api/comments/:commentId/replies
// @access  Private
router.get('/:commentId/replies', 
  validate(PostValidator.commentIdValidation()), 
  CommentController.getCommentReplies
);

// @desc    Get user's comments
// @route   GET /api/comments/user/:userId
// @access  Private
router.get('/user/:userId', 
  validate(PostValidator.userIdValidation()), 
  CommentController.getUserComments
);

module.exports = router;
