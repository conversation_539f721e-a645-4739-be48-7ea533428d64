const express = require('express');
const AuthController = require('../controllers/authController');
const AuthValidator = require('../validators/authValidator');
const { validate } = require('../validators');
const { protect } = require('../middleware/auth');

const router = express.Router();

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
router.post('/register',
  validate(AuthValidator.registerValidation()),
  AuthController.register
);

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
router.post('/login',
  validate(AuthValidator.loginValidation()),
  AuthController.login
);

// @desc    Get current user profile
// @route   GET /api/auth/profile
// @access  Private
router.get('/profile',
  protect,
  AuthController.getProfile
);

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
router.put('/profile',
  protect,
  validate(AuthValidator.updateProfileValidation()),
  AuthController.updateProfile
);

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
router.put('/change-password',
  protect,
  validate(AuthValidator.changePasswordValidation()),
  AuthController.changePassword
);

module.exports = router;
