const mongoose = require('mongoose');

const followSchema = new mongoose.Schema({
  follower: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Follower is required']
  },
  following: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Following user is required']
  },
  tenant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Tenant',
    required: [true, 'Tenant is required']
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'blocked'],
    default: 'accepted'
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index to ensure one follow relationship per user pair
followSchema.index({ follower: 1, following: 1 }, { unique: true });

// Additional indexes for performance
followSchema.index({ follower: 1, status: 1, isActive: 1 });
followSchema.index({ following: 1, status: 1, isActive: 1 });
followSchema.index({ tenant: 1, createdAt: -1 });

// Validation to prevent self-following
followSchema.pre('save', function(next) {
  if (this.follower.toString() === this.following.toString()) {
    const error = new Error('Users cannot follow themselves');
    return next(error);
  }
  next();
});

// Static method to toggle follow
followSchema.statics.toggleFollow = async function(followerId, followingId, tenantId) {
  try {
    const existingFollow = await this.findOne({ 
      follower: followerId, 
      following: followingId 
    });
    
    if (existingFollow) {
      if (existingFollow.isActive) {
        // Unfollow
        existingFollow.isActive = false;
        await existingFollow.save();
        
        // Update follower and following counts
        await mongoose.model('User').findByIdAndUpdate(
          followerId,
          { $inc: { followingCount: -1 } }
        );
        await mongoose.model('User').findByIdAndUpdate(
          followingId,
          { $inc: { followersCount: -1 } }
        );
        
        return { action: 'unfollowed', follow: existingFollow };
      } else {
        // Re-follow
        existingFollow.isActive = true;
        existingFollow.status = 'accepted';
        await existingFollow.save();
        
        // Update follower and following counts
        await mongoose.model('User').findByIdAndUpdate(
          followerId,
          { $inc: { followingCount: 1 } }
        );
        await mongoose.model('User').findByIdAndUpdate(
          followingId,
          { $inc: { followersCount: 1 } }
        );
        
        return { action: 'followed', follow: existingFollow };
      }
    } else {
      // Create new follow
      const newFollow = await this.create({
        follower: followerId,
        following: followingId,
        tenant: tenantId,
        status: 'accepted'
      });
      
      // Update follower and following counts
      await mongoose.model('User').findByIdAndUpdate(
        followerId,
        { $inc: { followingCount: 1 } }
      );
      await mongoose.model('User').findByIdAndUpdate(
        followingId,
        { $inc: { followersCount: 1 } }
      );
      
      return { action: 'followed', follow: newFollow };
    }
  } catch (error) {
    throw new Error(`Error toggling follow: ${error.message}`);
  }
};

// Static method to check if user is following another user
followSchema.statics.isFollowing = async function(followerId, followingId) {
  const follow = await this.findOne({
    follower: followerId,
    following: followingId,
    status: 'accepted',
    isActive: true
  });
  return !!follow;
};

// Static method to get followers
followSchema.statics.getFollowers = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    tenantId = null
  } = options;

  const skip = (page - 1) * limit;
  const query = { 
    following: userId, 
    status: 'accepted', 
    isActive: true 
  };
  
  if (tenantId) {
    query.tenant = tenantId;
  }

  return this.find(query)
    .populate('follower', 'name username avatar bio followersCount followingCount')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get following
followSchema.statics.getFollowing = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    tenantId = null
  } = options;

  const skip = (page - 1) * limit;
  const query = { 
    follower: userId, 
    status: 'accepted', 
    isActive: true 
  };
  
  if (tenantId) {
    query.tenant = tenantId;
  }

  return this.find(query)
    .populate('following', 'name username avatar bio followersCount followingCount')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get mutual followers
followSchema.statics.getMutualFollowers = async function(userId1, userId2) {
  const user1Followers = await this.find({
    following: userId1,
    status: 'accepted',
    isActive: true
  }).select('follower');

  const user2Followers = await this.find({
    following: userId2,
    status: 'accepted',
    isActive: true
  }).select('follower');

  const user1FollowerIds = user1Followers.map(f => f.follower.toString());
  const user2FollowerIds = user2Followers.map(f => f.follower.toString());

  const mutualFollowerIds = user1FollowerIds.filter(id => 
    user2FollowerIds.includes(id)
  );

  return this.find({
    follower: { $in: mutualFollowerIds },
    following: userId1,
    status: 'accepted',
    isActive: true
  }).populate('follower', 'name username avatar');
};

// Static method to get follow suggestions
followSchema.statics.getFollowSuggestions = async function(userId, tenantId, limit = 10) {
  // Get users that the current user's followers are following
  // but the current user is not following yet
  const userFollowing = await this.find({
    follower: userId,
    status: 'accepted',
    isActive: true
  }).select('following');

  const followingIds = userFollowing.map(f => f.following.toString());
  followingIds.push(userId.toString()); // Exclude self

  const suggestions = await mongoose.model('User').aggregate([
    {
      $match: {
        _id: { $nin: followingIds.map(id => mongoose.Types.ObjectId(id)) },
        'tenants.tenant': mongoose.Types.ObjectId(tenantId),
        isActive: true
      }
    },
    {
      $lookup: {
        from: 'follows',
        localField: '_id',
        foreignField: 'following',
        as: 'followers'
      }
    },
    {
      $addFields: {
        mutualFollowersCount: {
          $size: {
            $filter: {
              input: '$followers',
              cond: {
                $and: [
                  { $eq: ['$$this.isActive', true] },
                  { $eq: ['$$this.status', 'accepted'] },
                  { $in: ['$$this.follower', followingIds.map(id => mongoose.Types.ObjectId(id))] }
                ]
              }
            }
          }
        }
      }
    },
    {
      $sort: { mutualFollowersCount: -1, followersCount: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        name: 1,
        username: 1,
        avatar: 1,
        bio: 1,
        followersCount: 1,
        mutualFollowersCount: 1
      }
    }
  ]);

  return suggestions;
};

module.exports = mongoose.model('Follow', followSchema);
