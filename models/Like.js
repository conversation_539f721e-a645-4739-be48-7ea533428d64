const mongoose = require('mongoose');

const likeSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required for like']
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
    required: [true, 'Post is required for like']
  },
  tenant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Tenant',
    required: [true, 'Tenant is required for like']
  },
  type: {
    type: String,
    enum: ['like', 'love', 'laugh', 'angry', 'sad', 'wow'],
    default: 'like'
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index to ensure one like per user per post
likeSchema.index({ user: 1, post: 1 }, { unique: true });

// Additional indexes for performance
likeSchema.index({ post: 1, isActive: 1 });
likeSchema.index({ user: 1, createdAt: -1 });
likeSchema.index({ tenant: 1, createdAt: -1 });

// Static method to toggle like
likeSchema.statics.toggleLike = async function(userId, postId, tenantId, type = 'like') {
  try {
    const existingLike = await this.findOne({ user: userId, post: postId });
    
    if (existingLike) {
      if (existingLike.isActive) {
        // Unlike the post
        existingLike.isActive = false;
        await existingLike.save();
        
        // Decrease like count in post
        await mongoose.model('Post').findByIdAndUpdate(
          postId,
          { $inc: { 'stats.likesCount': -1 } }
        );
        
        return { action: 'unliked', like: existingLike };
      } else {
        // Re-like the post
        existingLike.isActive = true;
        existingLike.type = type;
        await existingLike.save();
        
        // Increase like count in post
        await mongoose.model('Post').findByIdAndUpdate(
          postId,
          { $inc: { 'stats.likesCount': 1 } }
        );
        
        return { action: 'liked', like: existingLike };
      }
    } else {
      // Create new like
      const newLike = await this.create({
        user: userId,
        post: postId,
        tenant: tenantId,
        type: type
      });
      
      // Increase like count in post
      await mongoose.model('Post').findByIdAndUpdate(
        postId,
        { $inc: { 'stats.likesCount': 1 } }
      );
      
      return { action: 'liked', like: newLike };
    }
  } catch (error) {
    throw new Error(`Error toggling like: ${error.message}`);
  }
};

// Static method to get likes for a post
likeSchema.statics.getPostLikes = function(postId, options = {}) {
  const {
    page = 1,
    limit = 20,
    type = null
  } = options;

  const skip = (page - 1) * limit;
  const query = { post: postId, isActive: true };
  
  if (type) {
    query.type = type;
  }

  return this.find(query)
    .populate('user', 'name username avatar')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get user's likes
likeSchema.statics.getUserLikes = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    tenantId = null
  } = options;

  const skip = (page - 1) * limit;
  const query = { user: userId, isActive: true };
  
  if (tenantId) {
    query.tenant = tenantId;
  }

  return this.find(query)
    .populate('post', 'content author createdAt stats')
    .populate('tenant', 'name slug')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get like statistics
likeSchema.statics.getLikeStats = async function(postId) {
  const stats = await this.aggregate([
    {
      $match: { post: mongoose.Types.ObjectId(postId), isActive: true }
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 }
      }
    }
  ]);

  const result = {
    total: 0,
    like: 0,
    love: 0,
    laugh: 0,
    angry: 0,
    sad: 0,
    wow: 0
  };

  stats.forEach(stat => {
    result[stat._id] = stat.count;
    result.total += stat.count;
  });

  return result;
};

module.exports = mongoose.model('Like', likeSchema);
