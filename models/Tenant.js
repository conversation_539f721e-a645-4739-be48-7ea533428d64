const mongoose = require('mongoose');

const tenantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Tenant name is required'],
    trim: true,
    maxlength: [100, 'Tenant name cannot exceed 100 characters']
  },
  slug: {
    type: String,
    required: [true, 'Tenant slug is required'],
    unique: true,
    lowercase: true,
    trim: true,
    minlength: [3, 'Slug must be at least 3 characters'],
    maxlength: [50, 'Slug cannot exceed 50 characters'],
    match: [/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot exceed 1000 characters'],
    trim: true
  },
  logo: {
    type: String,
    default: null
  },
  coverImage: {
    type: String,
    default: null
  },
  website: {
    type: String,
    maxlength: [200, 'Website URL cannot exceed 200 characters'],
    trim: true
  },
  industry: {
    type: String,
    maxlength: [100, 'Industry cannot exceed 100 characters'],
    trim: true
  },
  location: {
    type: String,
    maxlength: [200, 'Location cannot exceed 200 characters'],
    trim: true
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Tenant owner is required']
  },
  settings: {
    isPublic: {
      type: Boolean,
      default: true
    },
    allowMemberInvites: {
      type: Boolean,
      default: true
    },
    requireApproval: {
      type: Boolean,
      default: false
    },
    allowPublicPosts: {
      type: Boolean,
      default: true
    },
    maxMembers: {
      type: Number,
      default: 1000
    }
  },
  stats: {
    membersCount: {
      type: Number,
      default: 1
    },
    postsCount: {
      type: Number,
      default: 0
    },
    activeMembers: {
      type: Number,
      default: 1
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'cancelled'],
      default: 'active'
    },
    expiresAt: {
      type: Date
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isVerified: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Indexes for better performance
tenantSchema.index({ slug: 1 });
tenantSchema.index({ owner: 1 });
tenantSchema.index({ 'settings.isPublic': 1, isActive: 1 });
tenantSchema.index({ createdAt: -1 });

// Virtual for tenant URL
tenantSchema.virtual('url').get(function() {
  return `/tenant/${this.slug}`;
});

// Instance method to check if user is member
tenantSchema.methods.isMember = function(userId) {
  return this.members.some(member => 
    member.user.toString() === userId.toString() && member.isActive
  );
};

// Instance method to get member role
tenantSchema.methods.getMemberRole = function(userId) {
  const member = this.members.find(member => 
    member.user.toString() === userId.toString() && member.isActive
  );
  return member ? member.role : null;
};

// Static method to find public tenants
tenantSchema.statics.findPublic = function() {
  return this.find({ 
    'settings.isPublic': true, 
    isActive: true 
  }).sort({ createdAt: -1 });
};

// Pre-save middleware to generate slug if not provided
tenantSchema.pre('save', function(next) {
  if (!this.slug && this.name) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
  next();
});

module.exports = mongoose.model('Tenant', tenantSchema);
