const mongoose = require('mongoose');

const postSchema = new mongoose.Schema({
  content: {
    type: String,
    required: [true, 'Post content is required'],
    trim: true,
    maxlength: [5000, 'Post content cannot exceed 5000 characters']
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Post author is required']
  },
  tenant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Tenant',
    required: [true, 'Post tenant is required']
  },
  type: {
    type: String,
    enum: ['text', 'image', 'video', 'link', 'poll'],
    default: 'text'
  },
  media: [{
    type: {
      type: String,
      enum: ['image', 'video', 'document'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    size: {
      type: Number
    },
    mimeType: {
      type: String
    },
    thumbnail: {
      type: String
    }
  }],
  link: {
    url: {
      type: String
    },
    title: {
      type: String
    },
    description: {
      type: String
    },
    image: {
      type: String
    },
    domain: {
      type: String
    }
  },
  poll: {
    question: {
      type: String,
      maxlength: [500, 'Poll question cannot exceed 500 characters']
    },
    options: [{
      text: {
        type: String,
        required: true,
        maxlength: [200, 'Poll option cannot exceed 200 characters']
      },
      votes: {
        type: Number,
        default: 0
      },
      voters: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }]
    }],
    allowMultiple: {
      type: Boolean,
      default: false
    },
    expiresAt: {
      type: Date
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [50, 'Tag cannot exceed 50 characters']
  }],
  mentions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  visibility: {
    type: String,
    enum: ['public', 'tenant', 'followers', 'private'],
    default: 'tenant'
  },
  isEdited: {
    type: Boolean,
    default: false
  },
  editHistory: [{
    content: String,
    editedAt: {
      type: Date,
      default: Date.now
    }
  }],
  stats: {
    likesCount: {
      type: Number,
      default: 0
    },
    commentsCount: {
      type: Number,
      default: 0
    },
    sharesCount: {
      type: Number,
      default: 0
    },
    viewsCount: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPinned: {
    type: Boolean,
    default: false
  },
  isReported: {
    type: Boolean,
    default: false
  },
  reportCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better performance
postSchema.index({ author: 1, createdAt: -1 });
postSchema.index({ tenant: 1, createdAt: -1 });
postSchema.index({ visibility: 1, isActive: 1, createdAt: -1 });
postSchema.index({ tags: 1 });
postSchema.index({ 'stats.likesCount': -1 });
postSchema.index({ isPinned: -1, createdAt: -1 });

// Text index for search
postSchema.index({ 
  content: 'text', 
  tags: 'text' 
});

// Virtual for post URL
postSchema.virtual('url').get(function() {
  return `/post/${this._id}`;
});

// Instance method to check if user liked the post
postSchema.methods.isLikedBy = async function(userId) {
  const Like = mongoose.model('Like');
  const like = await Like.findOne({ 
    post: this._id, 
    user: userId, 
    isActive: true 
  });
  return !!like;
};

// Instance method to get excerpt
postSchema.methods.getExcerpt = function(length = 150) {
  if (this.content.length <= length) {
    return this.content;
  }
  return this.content.substring(0, length).trim() + '...';
};

// Static method to find posts by tenant
postSchema.statics.findByTenant = function(tenantId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = -1,
    visibility = ['public', 'tenant']
  } = options;

  const skip = (page - 1) * limit;

  return this.find({
    tenant: tenantId,
    visibility: { $in: visibility },
    isActive: true
  })
  .populate('author', 'name username avatar')
  .populate('tenant', 'name slug')
  .sort({ [sortBy]: sortOrder })
  .skip(skip)
  .limit(limit);
};

// Static method to find posts by author
postSchema.statics.findByAuthor = function(authorId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  const skip = (page - 1) * limit;

  return this.find({
    author: authorId,
    isActive: true
  })
  .populate('author', 'name username avatar')
  .populate('tenant', 'name slug')
  .sort({ [sortBy]: sortOrder })
  .skip(skip)
  .limit(limit);
};

// Pre-save middleware to extract mentions and tags
postSchema.pre('save', function(next) {
  // Extract mentions (@username)
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;
  while ((match = mentionRegex.exec(this.content)) !== null) {
    mentions.push(match[1]);
  }

  // Extract hashtags (#tag)
  const tagRegex = /#(\w+)/g;
  const tags = [];
  while ((match = tagRegex.exec(this.content)) !== null) {
    tags.push(match[1].toLowerCase());
  }

  this.tags = [...new Set(tags)]; // Remove duplicates
  next();
});

module.exports = mongoose.model('Post', postSchema);
