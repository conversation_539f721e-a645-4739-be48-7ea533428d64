const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
  content: {
    type: String,
    required: [true, 'Comment content is required'],
    trim: true,
    maxlength: [2000, 'Comment content cannot exceed 2000 characters']
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Comment author is required']
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
    required: [true, 'Post is required for comment']
  },
  tenant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Tenant',
    required: [true, 'Tenant is required for comment']
  },
  parentComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null
  },
  mentions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  media: [{
    type: {
      type: String,
      enum: ['image', 'gif'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    filename: {
      type: String,
      required: true
    }
  }],
  isEdited: {
    type: Boolean,
    default: false
  },
  editHistory: [{
    content: String,
    editedAt: {
      type: Date,
      default: Date.now
    }
  }],
  stats: {
    likesCount: {
      type: Number,
      default: 0
    },
    repliesCount: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isReported: {
    type: Boolean,
    default: false
  },
  reportCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better performance
commentSchema.index({ post: 1, createdAt: -1 });
commentSchema.index({ author: 1, createdAt: -1 });
commentSchema.index({ parentComment: 1, createdAt: 1 });
commentSchema.index({ tenant: 1, createdAt: -1 });

// Text index for search
commentSchema.index({ content: 'text' });

// Virtual for nested replies
commentSchema.virtual('replies', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentComment'
});

// Instance method to check if comment is a reply
commentSchema.methods.isReply = function() {
  return !!this.parentComment;
};

// Instance method to get comment depth
commentSchema.methods.getDepth = async function() {
  let depth = 0;
  let currentComment = this;
  
  while (currentComment.parentComment) {
    depth++;
    currentComment = await this.constructor.findById(currentComment.parentComment);
    if (!currentComment) break;
  }
  
  return depth;
};

// Static method to get comments for a post
commentSchema.statics.getPostComments = function(postId, options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = 1,
    includeReplies = false
  } = options;

  const skip = (page - 1) * limit;
  const query = { 
    post: postId, 
    isActive: true,
    parentComment: includeReplies ? { $exists: true } : null
  };

  return this.find(query)
    .populate('author', 'name username avatar')
    .populate('parentComment', 'content author')
    .sort({ [sortBy]: sortOrder })
    .skip(skip)
    .limit(limit);
};

// Static method to get comment replies
commentSchema.statics.getCommentReplies = function(commentId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 1
  } = options;

  const skip = (page - 1) * limit;

  return this.find({
    parentComment: commentId,
    isActive: true
  })
  .populate('author', 'name username avatar')
  .sort({ [sortBy]: sortOrder })
  .skip(skip)
  .limit(limit);
};

// Static method to get user's comments
commentSchema.statics.getUserComments = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    tenantId = null
  } = options;

  const skip = (page - 1) * limit;
  const query = { author: userId, isActive: true };
  
  if (tenantId) {
    query.tenant = tenantId;
  }

  return this.find(query)
    .populate('post', 'content author')
    .populate('tenant', 'name slug')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Pre-save middleware to extract mentions
commentSchema.pre('save', function(next) {
  // Extract mentions (@username)
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;
  while ((match = mentionRegex.exec(this.content)) !== null) {
    mentions.push(match[1]);
  }
  next();
});

// Post-save middleware to update comment count in post
commentSchema.post('save', async function(doc) {
  if (doc.isNew && doc.isActive) {
    // Increase comment count in post
    await mongoose.model('Post').findByIdAndUpdate(
      doc.post,
      { $inc: { 'stats.commentsCount': 1 } }
    );

    // If this is a reply, increase reply count in parent comment
    if (doc.parentComment) {
      await this.constructor.findByIdAndUpdate(
        doc.parentComment,
        { $inc: { 'stats.repliesCount': 1 } }
      );
    }
  }
});

// Post-remove middleware to update comment count
commentSchema.post('findOneAndUpdate', async function(doc) {
  if (doc && !doc.isActive) {
    // Decrease comment count in post
    await mongoose.model('Post').findByIdAndUpdate(
      doc.post,
      { $inc: { 'stats.commentsCount': -1 } }
    );

    // If this is a reply, decrease reply count in parent comment
    if (doc.parentComment) {
      await mongoose.model('Comment').findByIdAndUpdate(
        doc.parentComment,
        { $inc: { 'stats.repliesCount': -1 } }
      );
    }
  }
});

module.exports = mongoose.model('Comment', commentSchema);
