const { body, param } = require('express-validator');

/**
 * Post Validation Rules
 * Contains validation rules for post endpoints
 */

class PostValidator {
  /**
   * Validation rules for creating a post
   */
  static createPostValidation() {
    return [
      body('content')
        .trim()
        .notEmpty()
        .withMessage('Post content is required')
        .isLength({ min: 1, max: 5000 })
        .withMessage('Post content must be between 1 and 5000 characters'),

      body('type')
        .optional()
        .isIn(['text', 'image', 'video', 'link', 'poll'])
        .withMessage('Invalid post type'),

      body('visibility')
        .optional()
        .isIn(['public', 'tenant', 'followers', 'private'])
        .withMessage('Invalid visibility setting'),

      body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),

      body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Each tag must be between 1 and 50 characters')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Tags can only contain letters, numbers, and underscores'),

      body('media')
        .optional()
        .isArray()
        .withMessage('Media must be an array'),

      body('media.*.type')
        .optional()
        .isIn(['image', 'video', 'document'])
        .withMessage('Invalid media type'),

      body('media.*.url')
        .optional()
        .isURL()
        .withMessage('Invalid media URL'),

      body('link.url')
        .optional()
        .isURL()
        .withMessage('Invalid link URL'),

      body('poll.question')
        .optional()
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Poll question must be between 1 and 500 characters'),

      body('poll.options')
        .optional()
        .isArray({ min: 2, max: 10 })
        .withMessage('Poll must have between 2 and 10 options'),

      body('poll.options.*.text')
        .optional()
        .trim()
        .isLength({ min: 1, max: 200 })
        .withMessage('Poll option must be between 1 and 200 characters')
    ];
  }

  /**
   * Validation rules for updating a post
   */
  static updatePostValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid post ID'),

      body('content')
        .optional()
        .trim()
        .isLength({ min: 1, max: 5000 })
        .withMessage('Post content must be between 1 and 5000 characters'),

      body('visibility')
        .optional()
        .isIn(['public', 'tenant', 'followers', 'private'])
        .withMessage('Invalid visibility setting'),

      body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),

      body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Each tag must be between 1 and 50 characters')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Tags can only contain letters, numbers, and underscores')
    ];
  }

  /**
   * Validation rules for post ID parameter
   */
  static postIdValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid post ID'),

      param('postId')
        .optional()
        .isMongoId()
        .withMessage('Invalid post ID')
    ];
  }

  /**
   * Validation rules for user ID parameter
   */
  static userIdValidation() {
    return [
      param('userId')
        .isMongoId()
        .withMessage('Invalid user ID')
    ];
  }

  /**
   * Validation rules for creating a comment
   */
  static createCommentValidation() {
    return [
      param('postId')
        .isMongoId()
        .withMessage('Invalid post ID'),

      body('content')
        .trim()
        .notEmpty()
        .withMessage('Comment content is required')
        .isLength({ min: 1, max: 2000 })
        .withMessage('Comment content must be between 1 and 2000 characters'),

      body('parentComment')
        .optional()
        .isMongoId()
        .withMessage('Invalid parent comment ID'),

      body('media')
        .optional()
        .isArray()
        .withMessage('Media must be an array'),

      body('media.*.type')
        .optional()
        .isIn(['image', 'gif'])
        .withMessage('Invalid media type for comment'),

      body('media.*.url')
        .optional()
        .isURL()
        .withMessage('Invalid media URL')
    ];
  }

  /**
   * Validation rules for updating a comment
   */
  static updateCommentValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid comment ID'),

      body('content')
        .trim()
        .notEmpty()
        .withMessage('Comment content is required')
        .isLength({ min: 1, max: 2000 })
        .withMessage('Comment content must be between 1 and 2000 characters')
    ];
  }

  /**
   * Validation rules for comment ID parameter
   */
  static commentIdValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid comment ID'),

      param('commentId')
        .optional()
        .isMongoId()
        .withMessage('Invalid comment ID')
    ];
  }

  /**
   * Validation rules for like type
   */
  static likeValidation() {
    return [
      param('postId')
        .isMongoId()
        .withMessage('Invalid post ID'),

      body('type')
        .optional()
        .isIn(['like', 'love', 'laugh', 'angry', 'sad', 'wow'])
        .withMessage('Invalid like type')
    ];
  }

  /**
   * Validation rules for pagination
   */
  static paginationValidation() {
    return [
      body('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      body('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
    ];
  }
}

module.exports = PostValidator;
