const { body, param } = require('express-validator');

/**
 * Tenant Validation Rules
 * Contains validation rules for tenant endpoints
 */

class TenantValidator {
  /**
   * Validation rules for creating a tenant
   */
  static createTenantValidation() {
    return [
      body('name')
        .trim()
        .notEmpty()
        .withMessage('Tenant name is required')
        .isLength({ min: 2, max: 100 })
        .withMessage('Tenant name must be between 2 and 100 characters'),

      body('slug')
        .optional()
        .trim()
        .isLength({ min: 3, max: 50 })
        .withMessage('Slug must be between 3 and 50 characters')
        .matches(/^[a-z0-9-]+$/)
        .withMessage('Slug can only contain lowercase letters, numbers, and hyphens')
        .custom((value) => {
          if (value && (value.startsWith('-') || value.endsWith('-'))) {
            throw new Error('Slug cannot start or end with a hyphen');
          }
          return true;
        }),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

      body('website')
        .optional()
        .trim()
        .isURL()
        .withMessage('Please provide a valid website URL')
        .isLength({ max: 200 })
        .withMessage('Website URL cannot exceed 200 characters'),

      body('industry')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Industry cannot exceed 100 characters'),

      body('location')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Location cannot exceed 200 characters'),

      body('settings.isPublic')
        .optional()
        .isBoolean()
        .withMessage('isPublic must be a boolean'),

      body('settings.allowMemberInvites')
        .optional()
        .isBoolean()
        .withMessage('allowMemberInvites must be a boolean'),

      body('settings.requireApproval')
        .optional()
        .isBoolean()
        .withMessage('requireApproval must be a boolean'),

      body('settings.allowPublicPosts')
        .optional()
        .isBoolean()
        .withMessage('allowPublicPosts must be a boolean'),

      body('settings.maxMembers')
        .optional()
        .isInt({ min: 1, max: 10000 })
        .withMessage('maxMembers must be between 1 and 10000')
    ];
  }

  /**
   * Validation rules for updating a tenant
   */
  static updateTenantValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid tenant ID'),

      body('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Tenant name must be between 2 and 100 characters'),

      body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

      body('website')
        .optional()
        .trim()
        .isURL()
        .withMessage('Please provide a valid website URL')
        .isLength({ max: 200 })
        .withMessage('Website URL cannot exceed 200 characters'),

      body('industry')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Industry cannot exceed 100 characters'),

      body('location')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Location cannot exceed 200 characters'),

      body('settings.isPublic')
        .optional()
        .isBoolean()
        .withMessage('isPublic must be a boolean'),

      body('settings.allowMemberInvites')
        .optional()
        .isBoolean()
        .withMessage('allowMemberInvites must be a boolean'),

      body('settings.requireApproval')
        .optional()
        .isBoolean()
        .withMessage('requireApproval must be a boolean'),

      body('settings.allowPublicPosts')
        .optional()
        .isBoolean()
        .withMessage('allowPublicPosts must be a boolean'),

      body('settings.maxMembers')
        .optional()
        .isInt({ min: 1, max: 10000 })
        .withMessage('maxMembers must be between 1 and 10000')
    ];
  }

  /**
   * Validation rules for tenant ID parameter
   */
  static tenantIdValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid tenant ID')
    ];
  }

  /**
   * Validation rules for tenant search
   */
  static searchValidation() {
    return [
      body('search')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query must be between 1 and 100 characters'),

      body('industry')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Industry filter cannot exceed 100 characters'),

      body('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

      body('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
    ];
  }

  /**
   * Validation rules for member management
   */
  static memberValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid tenant ID'),

      param('userId')
        .optional()
        .isMongoId()
        .withMessage('Invalid user ID'),

      body('role')
        .optional()
        .isIn(['member', 'admin', 'owner'])
        .withMessage('Invalid role. Must be member, admin, or owner')
    ];
  }

  /**
   * Validation rules for tenant invitation
   */
  static inviteValidation() {
    return [
      param('id')
        .isMongoId()
        .withMessage('Invalid tenant ID'),

      body('email')
        .trim()
        .notEmpty()
        .withMessage('Email is required')
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),

      body('role')
        .optional()
        .isIn(['member', 'admin'])
        .withMessage('Invalid role. Must be member or admin'),

      body('message')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Invitation message cannot exceed 500 characters')
    ];
  }
}

module.exports = TenantValidator;
