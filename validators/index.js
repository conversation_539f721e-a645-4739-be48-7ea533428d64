const { validationResult } = require('express-validator');
const ResponseController = require('../controllers/responseController');

/**
 * Common Validator Middleware
 * Handles validation result checking
 */

/**
 * Middleware to handle validation results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));

    return ResponseController.validationError(res, formattedErrors);
  }
  
  next();
};

/**
 * Create validation middleware chain
 * @param {Array} validationRules - Array of validation rules
 * @returns {Array} - Array containing validation rules and error handler
 */
const validate = (validationRules) => {
  return [...validationRules, handleValidationErrors];
};

module.exports = {
  handleValidationErrors,
  validate
};
